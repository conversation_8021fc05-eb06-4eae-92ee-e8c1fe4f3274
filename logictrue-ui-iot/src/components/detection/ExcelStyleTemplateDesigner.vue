<template>
  <div class="excel-style-template-designer">
    <div class="designer-header">
      <el-form :model="templateInfo" label-width="100px" size="small">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="模板名称">
              <el-input v-model="templateInfo.name" placeholder="请输入模板名称"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="模板编码">
              <el-input v-model="templateInfo.code" placeholder="请输入模板编码"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="设备类型">
              <el-select v-model="templateInfo.deviceType" placeholder="请选择设备类型" style="width: 100%">
                <el-option label="输送设备" value="conveyor"></el-option>
                <el-option label="起重设备" value="crane"></el-option>
                <el-option label="泵类设备" value="pump"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <div class="designer-body">
      <el-row :gutter="20">
        <!-- 左侧：字段库 -->
        <el-col :span="4">
          <div class="field-library">
            <div class="library-header">
              <h4>字段库</h4>
            </div>
            <div class="library-content">
              <el-collapse v-model="activeLibrary">
                <el-collapse-item title="基础字段" name="basic">
                  <div class="field-list">
                    <div
                      v-for="field in basicFieldLibrary"
                      :key="field.type"
                      class="field-item"
                      draggable="true"
                      @dragstart="handleDragStart($event, field, 'basic')"
                    >
                      <i :class="field.icon"></i>
                      <span>{{ field.name }}</span>
                    </div>
                  </div>
                </el-collapse-item>
                <el-collapse-item title="表格字段" name="table">
                  <div class="field-list">
                    <div
                      v-for="field in tableFieldLibrary"
                      :key="field.type"
                      class="field-item"
                      draggable="true"
                      @dragstart="handleDragStart($event, field, 'table')"
                    >
                      <i :class="field.icon"></i>
                      <span>{{ field.name }}</span>
                    </div>
                  </div>
                </el-collapse-item>
              </el-collapse>
            </div>
          </div>
        </el-col>

        <!-- 右侧：Excel样式设计区域 -->
        <el-col :span="20">
          <div class="excel-design-area">
            <div class="excel-toolbar">
              <div class="toolbar-left">
                <el-button-group size="mini">
                  <el-button @click="clearAll">清空</el-button>
                </el-button-group>
                <el-button-group size="mini" style="margin-left: 10px;">
                  <el-button
                    @click="mergeCells"
                    :disabled="selectedCells.length < 2"
                    icon="el-icon-s-grid"
                  >
                    合并单元格
                  </el-button>
                  <el-button
                    @click="splitCells"
                    :disabled="!hasSelectedMergedCell"
                    icon="el-icon-menu"
                  >
                    拆分单元格
                  </el-button>
                </el-button-group>
              </div>
              <div class="toolbar-right">
                <div class="selection-info" v-if="selectedCells.length > 0">
                  已选择 {{ selectedCells.length }} 个单元格
                </div>
                <el-button size="mini" @click="testContextMenu">测试右键菜单</el-button>
                <el-button size="mini" @click="handleExportExcel">导出Excel</el-button>
                <el-button type="primary" size="mini" @click="handleSave">保存模板</el-button>
              </div>
            </div>

            <!-- Sheet标签页 -->
            <div class="sheet-tabs">
              <el-tabs
                :value="activeSheetId"
                type="card"
                closable
                @tab-remove="removeSheet"
                @tab-click="switchSheet"
              >
                <el-tab-pane
                  v-for="sheet in sheets"
                  :key="sheet.id"
                  :name="sheet.id"
                >
                  <span
                    slot="label"
                    @dblclick="startRenameSheet(sheet)"
                    :title="'双击重命名 ' + sheet.name"
                  >
                    {{ sheet.name }}
                  </span>
                </el-tab-pane>
              </el-tabs>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-plus"
                @click="addSheet"
                class="add-sheet-btn"
              >
                添加Sheet
              </el-button>
            </div>

            <div class="excel-sheet-container">
              <div class="excel-sheet" :style="{ width: Math.max(totalTableWidth, 3000) + 'px', minHeight: '1000px' }">
                <!-- 列标题 A, B, C, D... -->
                <div class="column-headers">
                  <div class="row-header-cell"></div>
                  <div
                    v-for="(col, index) in maxColumns"
                    :key="index"
                    class="column-header-cell"
                    :style="{ width: columnWidths[index] + 'px' }"
                  >
                    <div class="column-header-content">
                      <span>{{ getColumnLabel(index) }}</span>
                      <el-popover
                        placement="bottom"
                        width="200"
                        trigger="click"
                      >
                        <div class="width-editor">
                          <el-form size="mini">
                            <el-form-item label="列宽(磅)">
                              <el-input-number
                                v-model="columnWidths[index]"
                                :min="36"
                                :max="360"
                                :step="6"
                                size="mini"
                                @change="updateColumnWidth(index)"
                              />
                              <span style="margin-left: 5px;">pt</span>
                              <div style="margin-top: 5px; font-size: 12px; color: #909399;">
                                建议范围：36-360磅
                              </div>
                            </el-form-item>
                          </el-form>
                        </div>
                        <i slot="reference" class="el-icon-setting column-setting-icon"></i>
                      </el-popover>
                    </div>
                  </div>
                </div>

                <!-- Excel行 -->
                <div class="excel-rows">
                  <div
                    v-for="(row, rowIndex) in excelRows"
                    :key="rowIndex"
                    class="excel-row"
                  >
                    <!-- 行号 -->
                    <div class="row-header-cell">{{ rowIndex + 1 }}</div>

                    <!-- 单元格 -->
                    <div
                      v-for="(cell, colIndex) in row.cells"
                      :key="colIndex"
                      class="excel-cell"
                      :class="{
                        'has-content': cell.content && cell.content.trim(),
                        'is-label': cell.type === 'label',
                        'is-value': cell.type === 'value',
                        'is-header': cell.type === 'header',
                        'drag-over': dragOverCell === `${rowIndex}-${colIndex}`,
                        'drag-over-merged': dragOverCell === `${rowIndex}-${colIndex}-merged`,
                        'drag-preview-value': dragPreviewValueCell === `${rowIndex}-${colIndex}`,
                        'selected': isSelectedCell(rowIndex, colIndex),
                        'merged': cell.merged,
                        'merge-main': cell.mergeMain,
                        'hidden': cell.hidden
                      }"
                      :style="getCellStyle(cell, colIndex)"
                      :data-row-span="cell.mergeRowSpan || 1"
                      :data-col-span="cell.mergeColSpan || 1"
                      @drop="handleCellDrop($event, rowIndex, colIndex)"
                      @dragover.prevent="handleCellDragOver($event, rowIndex, colIndex)"
                      @dragenter.prevent="handleCellDragEnter($event, rowIndex, colIndex)"
                      @dragleave="handleCellDragLeave($event, rowIndex, colIndex)"
                      @click="selectCell(rowIndex, colIndex, $event)"
                      @contextmenu.prevent="showCellContextMenu($event, rowIndex, colIndex)"
                      @mousedown="handleMouseDown($event, rowIndex, colIndex)"
                    >
                      <div v-if="cell.content && cell.content.trim() && !(cell.merged && cell.mergeMain)" class="cell-content">
                        <span class="cell-text">{{ cell.content }}</span>
                        <div class="cell-actions">
                          <el-button type="text" size="mini" @click.stop="editCell(rowIndex, colIndex)">
                            <i class="el-icon-edit"></i>
                          </el-button>
                          <el-button type="text" size="mini" @click.stop="clearCell(rowIndex, colIndex)">
                            <i class="el-icon-delete"></i>
                          </el-button>
                          <el-button
                            v-if="cell.merged"
                            type="text"
                            size="mini"
                            @click.stop="splitSingleCell(rowIndex, colIndex)"
                            title="拆分此合并单元格"
                          >
                            <i class="el-icon-menu"></i>
                          </el-button>
                        </div>
                      </div>
                      <div v-else class="empty-cell">
                        <span class="cell-placeholder">
                          {{ getColumnLabel(colIndex) }}{{ rowIndex + 1 }}
                          <span v-if="cell.merged && !(cell.merged && cell.mergeMain)" class="merge-indicator">[合并]</span>
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- 合并单元格覆盖层 -->
                <div class="merged-cells-overlay" v-show="hasMergedCells">
                  <div
                    v-for="(row, rowIndex) in excelRows"
                    :key="`overlay-${rowIndex}`"
                  >
                    <div
                      v-for="(cell, colIndex) in row.cells"
                      :key="`overlay-${rowIndex}-${colIndex}`"
                      v-if="cell.merged && cell.mergeMain"
                      class="merged-cell-overlay excel-cell merged merge-main"
                      :style="getMergedOverlayStyle(rowIndex, colIndex)"
                      @click="selectCell(rowIndex, colIndex, $event)"
                      @contextmenu.prevent="showCellContextMenu($event, rowIndex, colIndex)"
                    >
                      <div class="cell-content">
                        <span class="cell-text">{{ cell.content }}</span>
                      </div>
                      <div class="cell-actions">
                        <el-button type="text" size="mini" @click.stop="editCell(rowIndex, colIndex)">
                          <i class="el-icon-edit"></i>
                        </el-button>
                        <el-button type="text" size="mini" @click.stop="clearCell(rowIndex, colIndex)">
                          <i class="el-icon-delete"></i>
                        </el-button>
                        <el-button
                          type="text"
                          size="mini"
                          @click.stop="splitSingleCell(rowIndex, colIndex)"
                          title="拆分此合并单元格"
                        >
                          <i class="el-icon-menu"></i>
                        </el-button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 右键菜单 -->
    <div
      v-if="contextMenuVisible"
      class="context-menu"
      :style="{ left: contextMenuX + 'px', top: contextMenuY + 'px' }"
      @click.stop
    >
      <div class="context-menu-item" @click="mergeCells" v-if="selectedCells.length > 1">
        <i class="el-icon-s-grid"></i>
        合并单元格
      </div>
      <div class="context-menu-item" @click="splitCells" v-if="hasSelectedMergedCell">
        <i class="el-icon-menu"></i>
        拆分单元格
      </div>
<!--      <div class="context-menu-divider" v-if="selectedCells.length > 0"></div>-->
      <div class="context-menu-item" @click="editSelectedCell" v-if="selectedCells.length === 1">
        <i class="el-icon-edit"></i>
        编辑单元格
      </div>
      <div class="context-menu-item" @click="clearSelectedCells" v-if="selectedCells.length > 0">
        <i class="el-icon-delete"></i>
        清空单元格
      </div>

      <!-- 始终显示的测试项 -->
      <div class="context-menu-item" @click="hideContextMenu">
        <i class="el-icon-close"></i>
        关闭菜单
      </div>
    </div>

    <!-- 单元格编辑对话框 -->
    <el-dialog
      title="编辑单元格"
      :visible.sync="cellEditVisible"
      width="500px"
      :modal="false"
      :modal-append-to-body="true"
      :close-on-click-modal="true"
      :close-on-press-escape="true"
      center
    >
      <el-form :model="editingCell" label-width="80px" size="small">
        <el-form-item label="内容">
          <el-input
            v-model="editingCell.content"
            placeholder="请输入内容"
            :maxlength="50"
            show-word-limit
          ></el-input>
        </el-form-item>
        <el-form-item label="类型">
          <el-select v-model="editingCell.type" style="width: 100%">
            <el-option label="标签(Label)" value="label"></el-option>
            <el-option label="值(Value)" value="value"></el-option>
            <el-option label="表头(Header)" value="header"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="说明">
          <el-alert
            :title="getCellTypeDescription(editingCell.type)"
            type="info"
            :closable="false"
            show-icon
          />
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="cellEditVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmCellEdit">确定</el-button>
      </div>
    </el-dialog>

    <!-- 调试信息 -->
    <div v-if="showDebug" class="debug-panel">
      <div class="debug-header">
        <div class="debug-title">
          <i class="el-icon-data-analysis"></i>
          <span>Excel设计器调试信息</span>
        </div>
        <div class="debug-actions">
          <el-button type="text" size="small" @click="toggleDebugSize">
            <i :class="isDebugExpanded ? 'el-icon-minus' : 'el-icon-plus'"></i>
            {{ isDebugExpanded ? '收起' : '展开' }}
          </el-button>
          <el-button type="text" size="small" @click="showDebug = false">
            <i class="el-icon-close"></i>
            关闭
          </el-button>
        </div>
      </div>
        <div class="debug-content" :class="{ 'expanded': isDebugExpanded }">
          <el-row :gutter="20">
            <!-- 左侧信息列 -->
            <el-col :span="8">
              <!-- 表格尺寸信息 -->
              <el-card class="debug-card" style="margin-bottom: 20px;">
                <div slot="header">
                  <i class="el-icon-s-grid"></i>
                  表格尺寸信息
                </div>
                <div class="debug-info">
                  <p><strong>当前Sheet:</strong> {{ currentSheetName }} ({{ sheets.length }}个Sheet)</p>
                  <p><strong>总宽度:</strong> {{ totalTableWidth }}px</p>
                  <p><strong>设置宽度:</strong> {{ Math.max(totalTableWidth, 3000) }}px</p>
                  <p><strong>容器宽度:</strong> 1600px</p>
                  <p><strong>列数:</strong> {{ maxColumns }} | <strong>行数:</strong> {{ maxRows }}</p>
                  <p><strong>横向滚动:</strong> {{ Math.max(totalTableWidth, 3000) > 1600 ? '✅显示' : '❌隐藏' }}</p>
                  <p><strong>列宽度:</strong> {{ columnWidths.slice(0, 3).join(', ') }}磅...</p>
                </div>
              </el-card>

              <!-- 模板配置信息 -->
              <el-card class="debug-card">
                <div slot="header">
                  <i class="el-icon-setting"></i>
                  模板配置信息
                </div>
                <div class="debug-info">
                  <p><strong>模板名称:</strong> {{ templateInfo.name || '未设置' }}</p>
                  <p><strong>模板编码:</strong> {{ templateInfo.code || '未设置' }}</p>
                  <p><strong>设备类型:</strong> {{ templateInfo.deviceType || '未选择' }}</p>
                  <p><strong>基础字段数:</strong> {{ templateConfig.basicFields.length }}</p>
                  <p><strong>表格字段数:</strong> {{ templateConfig.tableFields.length }}</p>
                  <p><strong>有内容单元格:</strong> {{ getCellsWithContent() }}</p>
                </div>
              </el-card>
            </el-col>

            <!-- 右侧Excel数据结构 -->
            <el-col :span="16">
              <el-card class="debug-card json-card">
                <div slot="header">
                  <i class="el-icon-document"></i>
                  Excel数据结构
                </div>
                <div class="json-viewer">
                  <el-tabs type="border-card" size="small">
                    <el-tab-pane label="模板数据" name="template">
                      <div class="json-container">
                        <pre>{{ getFormattedJson('template') }}</pre>
                      </div>
                    </el-tab-pane>
                    <el-tab-pane label="Excel布局" name="excel">
                      <div class="json-container">
                        <pre>{{ getFormattedJson('excel') }}</pre>
                      </div>
                    </el-tab-pane>
                    <el-tab-pane label="列宽配置" name="columns">
                      <div class="json-container">
                        <pre>{{ getFormattedJson('columns') }}</pre>
                      </div>
                    </el-tab-pane>
                    <el-tab-pane label="Sheets配置" name="sheets">
                      <div class="json-container">
                        <pre>{{ getFormattedJson('sheets') }}</pre>
                      </div>
                    </el-tab-pane>
                  </el-tabs>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
    </div>

    <!-- 调试按钮 -->
    <div v-if="!showDebug" class="debug-toggle-bottom">
      <el-button type="primary" size="medium" @click="handleDebugToggle" circle>
        <i class="el-icon-data-analysis"></i>
      </el-button>
      <div class="debug-tooltip">调试信息</div>
    </div>
  </div>
</template>

<script>
import {
  saveExcelTemplate,
  exportBlankExcelTemplate,
  checkTemplateCodeUnique,
  getExcelTemplate
} from '@/api/analysis/excelTemplate'

export default {
  name: 'ExcelStyleTemplateDesigner',
  props: {
    // 模板ID，用于编辑已有模板
    templateId: {
      type: [String, Number],
      default: null
    }
  },
  data() {
    return {
      showDebug: false,
      isDebugExpanded: false,
      activeLibrary: ['basic', 'table'],
      dragOverCell: null,
      selectedCell: null,
      selectedCells: [], // 多选单元格
      cellEditVisible: false,
      // 右键菜单相关
      contextMenuVisible: false,
      contextMenuX: 0,
      contextMenuY: 0,
      // 拖拽预览相关
      dragPreviewValueCell: null, // 预览值单元格位置
      currentDragData: null, // 当前拖拽的数据
      editingCell: {
        content: '',
        type: 'label'
      },
      editingCellPosition: null,
      // Sheet相关数据
      sheets: [],
      activeSheetId: null,
      sheetIdCounter: 1,

      // 当前sheet的数据（从sheets中获取）
      maxColumns: 20,
      maxRows: 30,
      columnWidths: [90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90], // 每列的宽度（磅）
      excelRows: [],

      templateInfo: {
        name: '检测模板',
        code: 'TEMPLATE_001',
        deviceType: ''
      },
      currentTemplateId: null, // 当前模板ID，保存后会设置
      templateConfig: {
        version: '1.0',
        basicFields: [],
        tableFields: []
      },
      basicFieldLibrary: [
        { type: 'text', name: '文本', icon: 'el-icon-edit-outline' }
      ],
      tableFieldLibrary: [
        { type: 'header', name: '表头', icon: 'el-icon-menu' }
      ]
    }
  },
  computed: {
    // 计算表格总宽度
    totalTableWidth() {
      const rowHeaderWidth = 40
      const totalColumnsWidth = this.columnWidths.reduce((sum, width) => sum + width, 0)
      return rowHeaderWidth + totalColumnsWidth
    },

    // 当前sheet名称
    currentSheetName() {
      const currentSheet = this.sheets.find(s => s.id === this.activeSheetId)
      return currentSheet ? currentSheet.name : 'Sheet1'
    },

    // 是否有选中的合并单元格
    hasSelectedMergedCell() {
      return this.selectedCells.some(cellKey => {
        const [rowIndex, colIndex] = cellKey.split('-').map(Number)
        return this.excelRows[rowIndex] &&
               this.excelRows[rowIndex].cells[colIndex] &&
               this.excelRows[rowIndex].cells[colIndex].merged
      })
    },

    // 是否存在合并单元格（用于控制覆盖层显示）
    hasMergedCells() {
      return this.excelRows.some(row =>
        row.cells.some(cell => cell.merged && cell.mergeMain)
      )
    }
  },
  async mounted() {
    await this.initializeComponent()

    // 注意：不在这里添加全局点击监听器，而是在显示菜单时动态添加

    // 添加全局拖拽结束事件监听器
    document.addEventListener('dragend', this.handleGlobalDragEnd)

    // 添加滚动和窗口大小改变事件监听器，用于隐藏右键菜单
    window.addEventListener('scroll', this.hideContextMenu, true)
    window.addEventListener('resize', this.hideContextMenu)
  },

  beforeDestroy() {
    // 移除全局事件监听器
    document.removeEventListener('click', this.hideContextMenu)
    document.removeEventListener('dragend', this.handleGlobalDragEnd)
    window.removeEventListener('scroll', this.hideContextMenu, true)
    window.removeEventListener('resize', this.hideContextMenu)
  },

  // 监听路由变化，确保每次进入页面都重新加载数据
  async activated() {
    await this.initializeComponent()
  },

  watch: {
    // 监听templateId变化，重新加载数据
    templateId: {
      handler: async function(newId, oldId) {
        if (newId !== oldId) {
          console.log('templateId变化，重新加载数据:', { oldId, newId })
          await this.initializeComponent()
        }
      },
      immediate: false
    }
  },
  methods: {
    // 统一的组件初始化方法
    async initializeComponent() {
      console.log('初始化组件，templateId:', this.templateId)

      // 重置组件状态
      this.resetComponentState()

      // 初始化第一个sheet
      this.initializeSheets()

      // 如果有模板ID，加载模板数据
      if (this.templateId) {
        await this.loadTemplate(this.templateId)
      } else {
        console.log('没有templateId，使用默认初始化')
      }

      // 确保横向滚动条出现
      this.$nextTick(() => {
        // this.ensureHorizontalScrollbar()
      })
    },

    // 重置组件状态
    resetComponentState() {
      console.log('重置组件状态')
      this.sheets = []
      this.activeSheetId = null
      this.selectedCell = null
      this.selectedCells = []
      this.cellEditVisible = false
      this.dragOverCell = null
      this.currentTemplateId = null
      this.contextMenuVisible = false
      this.contextMenuX = 0
      this.contextMenuY = 0

      // 重置模板信息
      this.templateInfo = {
        name: '检测模板',
        code: 'TEMPLATE_001',
        deviceType: ''
      }

      // 重置模板配置
      this.templateConfig = {
        version: '1.0',
        basicFields: [],
        tableFields: []
      }
    },

    // 初始化Sheets
    initializeSheets() {
      // 创建第一个sheet
      const firstSheet = this.createNewSheet('Sheet1')
      this.sheets = [firstSheet]
      this.activeSheetId = firstSheet.id
      this.loadSheetData(firstSheet)
    },

    // 创建新的sheet
    createNewSheet(name) {
      // 创建独立的列宽数组，避免引用共享
      const defaultColumnWidths = []
      for (let i = 0; i < 20; i++) {
        defaultColumnWidths.push(90)
      }

      const sheet = {
        id: this.generateUniqueSheetId(),
        name: name || this.generateUniqueSheetName(),
        maxColumns: 20,
        maxRows: 30,
        columnWidths: defaultColumnWidths,
        excelRows: []
      }

      // 初始化sheet的行数据
      for (let i = 0; i < sheet.maxRows; i++) {
        const row = { cells: [] }
        for (let j = 0; j < sheet.maxColumns; j++) {
          row.cells.push({
            content: '',
            type: '',
            fieldType: '',
            fieldCode: '',
            // 合并单元格相关属性
            merged: false,        // 是否为合并单元格
            mergeMain: false,     // 是否为合并单元格的主单元格
            mergeRowSpan: 1,      // 合并的行数
            mergeColSpan: 1,      // 合并的列数
            hidden: false         // 是否隐藏（被合并的单元格会被隐藏）
          })
        }
        sheet.excelRows.push(row)
      }

      return sheet
    },

    // 添加新sheet
    addSheet() {
      // 先保存当前sheet数据
      this.saveCurrentSheetData()

      const newSheetName = this.generateUniqueSheetName()
      const newSheet = this.createNewSheet(newSheetName)
      this.sheets.push(newSheet)
      this.activeSheetId = newSheet.id
      this.loadSheetData(newSheet)
      this.$message.success(`已添加 ${newSheetName}`)
    },

    // 生成唯一的Sheet ID
    generateUniqueSheetId() {
      const existingIds = this.sheets.map(sheet => sheet.id)
      let counter = 1
      let newId = `sheet_${counter}`

      // 找到第一个不重复的ID
      while (existingIds.includes(newId)) {
        counter++
        newId = `sheet_${counter}`
      }

      return newId
    },

    // 生成唯一的Sheet名称
    generateUniqueSheetName() {
      const existingNames = this.sheets.map(sheet => sheet.name)
      let counter = 1
      let newName = `Sheet${counter}`

      // 找到第一个不重复的名称
      while (existingNames.includes(newName)) {
        counter++
        newName = `Sheet${counter}`
      }

      return newName
    },

    // 开始重命名Sheet
    startRenameSheet(sheet) {
      this.$prompt('请输入新的Sheet名称:', '重命名Sheet', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue: sheet.name,
        inputValidator: (value) => {
          if (!value || !value.trim()) {
            return 'Sheet名称不能为空'
          }
          if (value.trim() !== sheet.name && this.sheets.some(s => s.name === value.trim())) {
            return 'Sheet名称已存在'
          }
          return true
        }
      }).then(({ value }) => {
        const newName = value.trim()
        if (newName !== sheet.name) {
          sheet.name = newName
          this.$message.success(`Sheet已重命名为: ${newName}`)
        }
      }).catch(() => {
        // 用户取消重命名
      })
    },

    // 删除sheet
    removeSheet(sheetId) {
      if (this.sheets.length <= 1) {
        this.$message.warning('至少需要保留一个Sheet')
        return
      }

      const sheetIndex = this.sheets.findIndex(sheet => sheet.id === sheetId)
      if (sheetIndex === -1) return

      const sheetName = this.sheets[sheetIndex].name
      this.sheets.splice(sheetIndex, 1)

      // 如果删除的是当前活动sheet，切换到第一个sheet
      if (this.activeSheetId === sheetId) {
        this.activeSheetId = this.sheets[0].id
        this.loadSheetData(this.sheets[0])
      }

      this.$message.success(`已删除 ${sheetName}`)
    },

    // 切换sheet
    switchSheet(tab) {
      const targetSheet = this.sheets.find(s => s.id === tab.name)
      if (targetSheet && tab.name !== this.activeSheetId) {
        console.log('切换前 - 当前activeSheetId:', this.activeSheetId)
        console.log('切换前 - 当前excelRows长度:', this.excelRows.length)
        console.log('切换前 - 当前excelRows第一行内容:', this.excelRows[0]?.cells[0]?.content)

        // 保存当前sheet数据到之前的sheet（使用旧的activeSheetId）
        const currentSheet = this.sheets.find(s => s.id === this.activeSheetId)
        if (currentSheet) {
          console.log('保存数据到sheet:', currentSheet.name, currentSheet.id)
          console.log('保存的excelRows长度:', this.excelRows.length)
          console.log('保存的第一行内容:', this.excelRows[0]?.cells[0]?.content)

          currentSheet.maxColumns = this.maxColumns
          currentSheet.maxRows = this.maxRows
          currentSheet.columnWidths = [...this.columnWidths]
          currentSheet.excelRows = JSON.parse(JSON.stringify(this.excelRows))

          console.log('保存后sheet的excelRows长度:', currentSheet.excelRows.length)
          console.log('保存后sheet的第一行内容:', currentSheet.excelRows[0]?.cells[0]?.content)
        }

        // 更新activeSheetId
        this.activeSheetId = tab.name

        console.log('切换到 - 目标sheet:', targetSheet.name, targetSheet.id)
        console.log('切换到 - 目标sheet excelRows长度:', targetSheet.excelRows.length)
        console.log('切换到 - 目标sheet第一行内容:', targetSheet.excelRows[0]?.cells[0]?.content)

        // 加载新sheet数据
        this.loadSheetData(targetSheet)

        console.log('切换后 - 当前excelRows长度:', this.excelRows.length)
        console.log('切换后 - 当前excelRows第一行内容:', this.excelRows[0]?.cells[0]?.content)
      }
    },

    // 保存当前sheet数据
    saveCurrentSheetData() {
      const currentSheet = this.sheets.find(s => s.id === this.activeSheetId)
      if (currentSheet) {
        console.log('保存数据到sheet:', currentSheet.name, currentSheet.id)
        console.log('保存的excelRows长度:', this.excelRows.length)
        console.log('保存的第一行内容:', this.excelRows[0]?.cells[0]?.content)

        currentSheet.maxColumns = this.maxColumns
        currentSheet.maxRows = this.maxRows
        currentSheet.columnWidths = [...this.columnWidths]
        currentSheet.excelRows = JSON.parse(JSON.stringify(this.excelRows))

        console.log('保存后sheet的excelRows长度:', currentSheet.excelRows.length)
        console.log('保存后sheet的第一行内容:', currentSheet.excelRows[0]?.cells[0]?.content)
      }
    },

    // 加载sheet数据到当前视图
    loadSheetData(sheet) {
      console.log('加载sheet数据:', sheet.name, sheet.id)
      console.log('加载的sheet excelRows长度:', sheet.excelRows.length)
      console.log('加载的sheet第一行内容:', sheet.excelRows[0]?.cells[0]?.content)

      this.maxColumns = sheet.maxColumns
      this.maxRows = sheet.maxRows
      this.columnWidths = [...sheet.columnWidths]
      this.excelRows = JSON.parse(JSON.stringify(sheet.excelRows))

      console.log('加载后当前excelRows长度:', this.excelRows.length)
      console.log('加载后当前第一行内容:', this.excelRows[0]?.cells[0]?.content)
    },

    // 初始化Excel表格
    initializeExcelSheet() {
      this.excelRows = []
      for (let i = 0; i < this.maxRows; i++) {
        const row = {
          cells: []
        }
        for (let j = 0; j < this.maxColumns; j++) {
          row.cells.push({
            content: '',
            type: '',
            fieldType: '',
            fieldCode: '',
            // 合并单元格相关属性
            merged: false,        // 是否为合并单元格
            mergeMain: false,     // 是否为合并单元格的主单元格
            mergeRowSpan: 1,      // 合并的行数
            mergeColSpan: 1,      // 合并的列数
            hidden: false         // 是否隐藏（被合并的单元格会被隐藏）
          })
        }
        this.excelRows.push(row)
      }
    },

    // 添加测试内容来确保滚动条出现
    addTestContent() {
      // 添加一些示例内容到不同位置，确保需要滚动
      if (this.excelRows.length > 5) {
        // 在右侧添加一些内容
        for (let i = 8; i < 12; i++) {
          if (this.excelRows[2] && this.excelRows[2].cells[i]) {
            this.excelRows[2].cells[i] = {
              content: `列${this.getColumnLabel(i)}内容`,
              type: 'label',
              fieldType: 'text',
              fieldCode: `test_${i}`
            }
          }
        }

        // 在底部添加一些内容
        for (let i = 20; i < 25; i++) {
          if (this.excelRows[i] && this.excelRows[i].cells[0]) {
            this.excelRows[i].cells[0] = {
              content: `行${i + 1}`,
              type: 'label',
              fieldType: 'text',
              fieldCode: `row_${i}`
            }
          }
        }
      }
    },

    // 获取列标签 A, B, C...
    getColumnLabel(index) {
      return String.fromCharCode(65 + index)
    },

    // 验证合并单元格数据一致性
    validateMergedCellsData() {
      console.log('开始验证合并单元格数据一致性')

      this.sheets.forEach((sheet, sheetIndex) => {
        console.log(`验证Sheet ${sheet.name} 的合并单元格数据`)

        sheet.excelRows.forEach((row, rowIndex) => {
          row.cells.forEach((cell, colIndex) => {
            if (cell.merged && cell.mergeMain) {
              console.log(`验证主合并单元格 ${this.getColumnLabel(colIndex)}${rowIndex + 1}:`, {
                content: cell.content,
                mergeRowSpan: cell.mergeRowSpan,
                mergeColSpan: cell.mergeColSpan,
                计算的结束列: colIndex + cell.mergeColSpan - 1,
                计算的value位置: this.getColumnLabel(colIndex + cell.mergeColSpan) + (rowIndex + 1)
              })

              // 验证被合并的单元格是否正确设置为hidden
              for (let r = rowIndex; r < rowIndex + cell.mergeRowSpan; r++) {
                for (let c = colIndex; c < colIndex + cell.mergeColSpan; c++) {
                  if (r === rowIndex && c === colIndex) continue // 跳过主单元格

                  const mergedCell = sheet.excelRows[r].cells[c]
                  if (!mergedCell.hidden || !mergedCell.merged) {
                    console.warn(`被合并单元格 ${this.getColumnLabel(c)}${r + 1} 状态不正确:`, mergedCell)
                  }
                }
              }
            }
          })
        })
      })
    },

    // 更新列宽度
    updateColumnWidth(columnIndex) {
      console.log(`更新列${this.getColumnLabel(columnIndex)}宽度为:`, this.columnWidths[columnIndex])
      // 强制更新视图
      this.$forceUpdate()
    },

    // 确保横向滚动条出现
    ensureHorizontalScrollbar() {
      const container = this.$el.querySelector('.excel-sheet-container')
      const sheet = this.$el.querySelector('.excel-sheet')

      if (container && sheet) {
        console.log('容器宽度:', container.clientWidth)
        console.log('表格宽度:', sheet.scrollWidth)
        console.log('计算总宽度:', this.totalTableWidth)

        // 如果表格宽度不够大，强制设置更大的宽度
        if (sheet.scrollWidth <= container.clientWidth) {
          console.log('强制设置表格宽度为3000px')
          sheet.style.width = '3000px'
        }
      }
    },

    // 获取单元格类型说明
    getCellTypeDescription(type) {
      switch (type) {
        case 'label':
          return '标签单元格：显示字段名称，如"检测人员"、"检测日期"等'
        case 'value':
          return '值单元格：显示字段值或占位符，如"张三"、"2024-01-15"等'
        case 'header':
          return '表头单元格：显示标题或分组信息，如"基础信息"、"检测数据"等'
        default:
          return '请选择单元格类型'
      }
    },

    // 统计有内容的单元格数量
    getCellsWithContent() {
      let count = 0
      this.excelRows.forEach(row => {
        row.cells.forEach(cell => {
          if (cell.content && cell.content.trim()) {
            count++
          }
        })
      })
      return count
    },

    // 获取Excel布局数据
    getExcelLayoutData() {
      const layoutData = {
        totalRows: this.maxRows,
        totalColumns: this.maxColumns,
        cellsWithContent: this.getCellsWithContent(),
        contentCells: []
      }

      this.excelRows.forEach((row, rowIndex) => {
        row.cells.forEach((cell, colIndex) => {
          if (cell.content && cell.content.trim()) {
            layoutData.contentCells.push({
              position: `${this.getColumnLabel(colIndex)}${rowIndex + 1}`,
              content: cell.content,
              type: cell.type,
              fieldType: cell.fieldType,
              fieldCode: cell.fieldCode
            })
          }
        })
      })

      return layoutData
    },

    // 获取列配置数据
    getColumnConfig() {
      return {
        totalColumns: this.maxColumns,
        columnWidths: this.columnWidths,
        totalWidth: this.totalTableWidth,
        averageWidth: Math.round(this.columnWidths.reduce((sum, width) => sum + width, 0) / this.columnWidths.length),
        columnLabels: Array.from({ length: this.maxColumns }, (_, i) => this.getColumnLabel(i))
      }
    },

    // 获取Sheets配置数据
    getSheetsConfig() {
      // 保存当前sheet数据
      this.saveCurrentSheetData()

      return {
        totalSheets: this.sheets.length,
        activeSheetId: this.activeSheetId,
        currentSheetName: this.currentSheetName,
        sheets: this.sheets.map(sheet => ({
          id: sheet.id,
          name: sheet.name,
          maxColumns: sheet.maxColumns,
          maxRows: sheet.maxRows,
          columnWidths: sheet.columnWidths,
          cellCount: sheet.excelRows.reduce((count, row) => {
            return count + row.cells.filter(cell => cell.content && cell.content.trim()).length
          }, 0),
          hasData: sheet.excelRows.some(row =>
            row.cells.some(cell => cell.content && cell.content.trim())
          )
        }))
      }
    },

    // 切换调试面板大小
    toggleDebugSize() {
      this.isDebugExpanded = !this.isDebugExpanded
      console.log('切换调试面板大小:', this.isDebugExpanded)
    },

    // 处理调试按钮点击
    handleDebugToggle() {
      console.log('点击调试按钮，当前showDebug:', this.showDebug)
      this.showDebug = true
      this.isDebugExpanded = true // 默认展开以便查看JSON数据
      console.log('设置后showDebug:', this.showDebug)
    },

    // 获取格式化的JSON数据
    getFormattedJson(type) {
      try {
        let data
        switch (type) {
          case 'template':
            data = this.generateTemplateData()
            break
          case 'excel':
            data = this.getExcelLayoutData()
            break
          case 'columns':
            data = this.getColumnConfig()
            break
          case 'sheets':
            data = this.getSheetsConfig()
            break
          default:
            data = { error: '未知的数据类型' }
        }
        return JSON.stringify(data, null, 2)
      } catch (error) {
        console.error('JSON格式化错误:', error)
        return JSON.stringify({
          error: '数据格式化失败',
          message: error.message,
          type: type
        }, null, 2)
      }
    },

    // 拖拽开始
    handleDragStart(event, field, category) {
      console.log('拖拽开始:', field, category)
      event.dataTransfer.effectAllowed = 'copy'
      const dragData = { ...field, category }
      event.dataTransfer.setData('text/plain', JSON.stringify(dragData))

      // 保存当前拖拽数据，用于预览
      this.currentDragData = dragData
    },

    // 全局拖拽结束处理
    handleGlobalDragEnd() {
      this.dragOverCell = null
      this.dragPreviewValueCell = null
      this.currentDragData = null
    },

    // 单元格拖拽进入
    handleCellDragEnter(event, rowIndex, colIndex) {
      event.preventDefault()

      const cell = this.excelRows[rowIndex].cells[colIndex]
      if (cell.merged && cell.mergeMain) {
        // 合并单元格的特殊处理
        this.dragOverCell = `${rowIndex}-${colIndex}-merged`
      } else if (!cell.hidden) {
        this.dragOverCell = `${rowIndex}-${colIndex}`
      }
    },

    // 单元格拖拽悬停
    handleCellDragOver(event, rowIndex, colIndex) {
      event.preventDefault()
      event.dataTransfer.dropEffect = 'copy'

      // 检查是否为合并单元格，如果是，显示特殊的悬浮样式
      const cell = this.excelRows[rowIndex].cells[colIndex]
      if (cell.merged && cell.mergeMain) {
        // 为合并单元格添加特殊的拖拽悬浮样式
        this.dragOverCell = `${rowIndex}-${colIndex}-merged`
        // 预览值单元格位置
        this.previewValueCellPosition(rowIndex, colIndex, cell)
      } else if (!cell.hidden) {
        this.dragOverCell = `${rowIndex}-${colIndex}`
        // 预览值单元格位置（普通单元格）
        this.previewValueCellPosition(rowIndex, colIndex, cell)
      }
    },

    // 预览值单元格位置
    previewValueCellPosition(rowIndex, colIndex, cell) {
      // 清除之前的预览
      this.dragPreviewValueCell = null

      // 检查当前拖拽的是否为基础字段
      if (this.currentDragData && this.currentDragData.category === 'basic') {
        if (cell.merged && cell.mergeMain) {
          // 合并单元格：值单元格在右侧
          const targetColIndex = colIndex + cell.mergeColSpan
          if (targetColIndex < this.maxColumns) {
            const targetCell = this.excelRows[rowIndex].cells[targetColIndex]
            // 检查目标位置是否可用
            if (!targetCell.hidden && (!targetCell.content || !targetCell.content.trim())) {
              this.dragPreviewValueCell = `${rowIndex}-${targetColIndex}`
            }
          }
        } else {
          // 普通单元格：值单元格在右侧
          const targetColIndex = colIndex + 1
          if (targetColIndex < this.maxColumns) {
            const targetCell = this.excelRows[rowIndex].cells[targetColIndex]
            // 检查目标位置是否可用
            if (!targetCell.hidden && (!targetCell.content || !targetCell.content.trim())) {
              this.dragPreviewValueCell = `${rowIndex}-${targetColIndex}`
            }
          }
        }
      }
    },

    // 单元格拖拽离开
    handleCellDragLeave(event, rowIndex, colIndex) {
      const rect = event.currentTarget.getBoundingClientRect()
      const x = event.clientX
      const y = event.clientY

      if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
        this.dragOverCell = null
        this.dragPreviewValueCell = null
        // 注意：这里不清除currentDragData，因为可能还在拖拽到其他单元格
      }
    },

    // 单元格拖拽放置
    handleCellDrop(event, rowIndex, colIndex) {
      event.preventDefault()
      this.dragOverCell = null
      this.dragPreviewValueCell = null
      this.currentDragData = null

      try {
        const fieldData = JSON.parse(event.dataTransfer.getData('text/plain'))
        console.log('拖拽到单元格:', rowIndex, colIndex, fieldData)

        if (!fieldData || !fieldData.name) {
          this.$message.error('拖拽数据无效')
          return
        }

        // 检查目标单元格是否被隐藏（被合并）
        const targetCell = this.excelRows[rowIndex].cells[colIndex]
        if (targetCell.hidden) {
          this.$message.warning('不能在被合并的单元格中放置字段')
          return
        }

        if (fieldData.type === 'header') {
          // 表头字段特殊处理
          if (!this.canPlaceHeaderInRow(rowIndex, colIndex)) {
            this.$message.warning('表头字段只能放置在同一行上！')
            return
          }

          // 保留原有的合并属性
          const newCell = {
            ...targetCell,
            content: fieldData.name,
            type: 'header',
            fieldType: fieldData.type,
            fieldCode: 'header_' + rowIndex + '_' + colIndex
          }

          this.$set(this.excelRows[rowIndex].cells, colIndex, newCell)
          // this.$message.success(`添加表头: ${fieldData.name}`)
        } else {
          // 基础字段处理（包含label和value）
          const result = this.handleBasicFieldDrop(fieldData, rowIndex, colIndex, targetCell)
          if (result.success) {
            // this.$message.success(result.message)
          } else {
            this.$message.warning(result.message)
          }
        }
      } catch (error) {
        console.error('解析拖拽数据失败:', error)
        this.$message.error('拖拽数据格式错误')
      }
    },

    // 处理基础字段拖拽放置
    handleBasicFieldDrop(fieldData, rowIndex, colIndex, targetCell) {
      const fieldCode = fieldData.type + '_' + Date.now()

      // 设置标签单元格，保留合并属性
      const labelCell = {
        ...targetCell,
        content: fieldData.name,
        type: 'label',
        fieldType: fieldData.type,
        fieldCode: fieldCode
      }

      this.$set(this.excelRows[rowIndex].cells, colIndex, labelCell)

      // 处理值单元格的放置，使用更新后的单元格信息
      const valueResult = this.placeValueCell(fieldData, fieldCode, rowIndex, colIndex, labelCell)

      if (valueResult.success) {
        return {
          success: true,
          message: `添加字段: ${fieldData.name}${valueResult.valueMessage ? ' (' + valueResult.valueMessage + ')' : ''}`
        }
      } else {
        return {
          success: true,
          message: `添加标签字段: ${fieldData.name}，${valueResult.message}`
        }
      }
    },

    // 放置值单元格
    placeValueCell(fieldData, fieldCode, labelRowIndex, labelColIndex, labelCell) {
      // 如果标签单元格是合并单元格
      if (labelCell.merged && labelCell.mergeMain) {
        return this.placeValueCellForMerged(fieldData, fieldCode, labelRowIndex, labelColIndex, labelCell)
      } else {
        return this.placeValueCellForNormal(fieldData, fieldCode, labelRowIndex, labelColIndex)
      }
    },

    // 为合并单元格放置值单元格（放在合并区域的右侧）
    placeValueCellForMerged(fieldData, fieldCode, labelRowIndex, labelColIndex, labelCell) {
      console.log('合并单元格值字段放置计算:', {
        labelPosition: `${this.getColumnLabel(labelColIndex)}${labelRowIndex + 1}`,
        labelColIndex,
        mergeColSpan: labelCell.mergeColSpan,
        mergeEndCol: labelColIndex + labelCell.mergeColSpan - 1,
        targetColIndex: labelColIndex + labelCell.mergeColSpan
      })

      const mergeEndCol = labelColIndex + labelCell.mergeColSpan - 1
      const targetRowIndex = labelRowIndex
      const targetColIndex = mergeEndCol + 1

      console.log('计算结果:', {
        mergeEndCol,
        targetRowIndex,
        targetColIndex,
        targetPosition: `${this.getColumnLabel(targetColIndex)}${targetRowIndex + 1}`
      })

      // 检查目标位置是否有效
      if (targetColIndex >= this.maxColumns) {
        return {
          success: false,
          message: '合并单元格右侧没有足够空间放置值字段'
        }
      }

      const targetCell = this.excelRows[targetRowIndex].cells[targetColIndex]

      // 检查目标单元格是否可用
      if (targetCell.hidden) {
        return {
          success: false,
          message: '合并单元格右侧的单元格被占用'
        }
      }

      if (targetCell.content && targetCell.content.trim()) {
        return {
          success: false,
          message: '合并单元格右侧的单元格已有内容'
        }
      }

      // 放置值单元格
      const valueCell = {
        ...targetCell,
        content: this.getFieldPlaceholder(fieldData),
        type: 'value',
        fieldType: fieldData.type,
        fieldCode: fieldCode + '_value'
      }

      this.$set(this.excelRows[targetRowIndex].cells, targetColIndex, valueCell)

      return {
        success: true,
        valueMessage: '值字段已放置在合并单元格右侧'
      }
    },

    // 为普通单元格放置值单元格（放在右侧）
    placeValueCellForNormal(fieldData, fieldCode, labelRowIndex, labelColIndex) {
      const targetColIndex = labelColIndex + 1

      // 检查右侧是否有空间
      if (targetColIndex >= this.maxColumns) {
        return {
          success: false,
          message: '右侧没有足够空间放置值字段'
        }
      }

      const targetCell = this.excelRows[labelRowIndex].cells[targetColIndex]

      // 检查目标单元格是否可用
      if (targetCell.hidden) {
        return {
          success: false,
          message: '右侧单元格被合并占用'
        }
      }

      if (targetCell.content && targetCell.content.trim()) {
        return {
          success: false,
          message: '右侧单元格已有内容'
        }
      }

      // 放置值单元格
      const valueCell = {
        ...targetCell,
        content: this.getFieldPlaceholder(fieldData),
        type: 'value',
        fieldType: fieldData.type,
        fieldCode: fieldCode + '_value'
      }

      this.$set(this.excelRows[labelRowIndex].cells, targetColIndex, valueCell)

      return {
        success: true,
        valueMessage: '值字段已放置在右侧'
      }
    },

    // 检查表头字段是否可以放置在指定行
    canPlaceHeaderInRow(targetRowIndex, targetColIndex) {
      // 检查是否已经有表头字段在其他行
      for (let rowIndex = 0; rowIndex < this.excelRows.length; rowIndex++) {
        if (rowIndex === targetRowIndex) continue // 跳过目标行

        for (let colIndex = 0; colIndex < this.excelRows[rowIndex].cells.length; colIndex++) {
          const cell = this.excelRows[rowIndex].cells[colIndex]
          if (cell.type === 'header' && cell.fieldType === 'header') {
            // 发现其他行有表头字段，不允许放置
            return false
          }
        }
      }

      // 检查目标位置是否已被占用
      const targetCell = this.excelRows[targetRowIndex].cells[targetColIndex]
      if (targetCell.content && targetCell.content.trim()) {
        return false
      }

      return true
    },

    // 获取字段占位符
    getFieldPlaceholder(field) {
      switch (field.type) {
        case 'text':
          return '请输入文本'
        case 'number':
          return '请输入数字'
        case 'date':
          return 'YYYY-MM-DD'
        case 'select':
          return '请选择'
        case 'textarea':
          return '请输入备注'
        default:
          return '请输入内容'
      }
    },

    // 选择单元格（支持多选）
    selectCell(rowIndex, colIndex, event) {
      // 如果是右键点击，不隐藏菜单
      if (event && event.button !== 2) {
        this.hideContextMenu()
      }

      const cellKey = `${rowIndex}-${colIndex}`

      if (event && (event.ctrlKey || event.metaKey)) {
        // Ctrl/Cmd + 点击：多选模式
        const index = this.selectedCells.indexOf(cellKey)
        if (index > -1) {
          this.selectedCells.splice(index, 1)
        } else {
          this.selectedCells.push(cellKey)
        }
      } else if (event && event.shiftKey && this.selectedCells.length > 0) {
        // Shift + 点击：范围选择
        this.selectCellRange(cellKey)
      } else {
        // 普通点击：单选
        this.selectedCells = [cellKey]
      }

      this.selectedCell = cellKey
      console.log('选择单元格:', rowIndex, colIndex, '当前选中:', this.selectedCells)
    },

    // 处理鼠标按下事件（用于调试右键点击）
    handleMouseDown(event, rowIndex, colIndex) {
      console.log('鼠标按下:', {
        button: event.button,
        which: event.which,
        rowIndex,
        colIndex,
        type: event.type
      })

      // 如果是右键点击（button === 2）
      if (event.button === 2) {
        console.log('检测到右键点击，准备显示菜单')
        // 不阻止默认行为，让contextmenu事件正常触发
      }
    },

    // 范围选择单元格
    selectCellRange(endCellKey) {
      if (this.selectedCells.length === 0) return

      const startCellKey = this.selectedCells[0]
      const [startRow, startCol] = startCellKey.split('-').map(Number)
      const [endRow, endCol] = endCellKey.split('-').map(Number)

      const minRow = Math.min(startRow, endRow)
      const maxRow = Math.max(startRow, endRow)
      const minCol = Math.min(startCol, endCol)
      const maxCol = Math.max(startCol, endCol)

      this.selectedCells = []
      for (let row = minRow; row <= maxRow; row++) {
        for (let col = minCol; col <= maxCol; col++) {
          this.selectedCells.push(`${row}-${col}`)
        }
      }
    },

    // 判断单元格是否被选中
    isSelectedCell(rowIndex, colIndex) {
      return this.selectedCells.includes(`${rowIndex}-${colIndex}`)
    },

    // 合并单元格
    mergeCells() {
      if (this.selectedCells.length < 2) {
        this.$message.warning('请选择至少2个单元格进行合并')
        return
      }

      // 解析选中的单元格坐标
      const cells = this.selectedCells.map(cellKey => {
        const [row, col] = cellKey.split('-').map(Number)
        return { row, col }
      })

      // 找到选中区域的边界
      const minRow = Math.min(...cells.map(c => c.row))
      const maxRow = Math.max(...cells.map(c => c.row))
      const minCol = Math.min(...cells.map(c => c.col))
      const maxCol = Math.max(...cells.map(c => c.col))

      // 检查是否为矩形区域
      const expectedCells = (maxRow - minRow + 1) * (maxCol - minCol + 1)
      if (cells.length !== expectedCells) {
        this.$message.warning('只能合并矩形区域的单元格')
        return
      }

      // 检查是否有已合并的单元格
      for (let row = minRow; row <= maxRow; row++) {
        for (let col = minCol; col <= maxCol; col++) {
          const cell = this.excelRows[row].cells[col]
          if (cell.merged) {
            this.$message.warning('选中区域包含已合并的单元格，请先拆分后再合并')
            return
          }
        }
      }

      // 收集所有单元格的内容
      let mergedContent = ''
      const contentParts = []

      for (let row = minRow; row <= maxRow; row++) {
        for (let col = minCol; col <= maxCol; col++) {
          const cell = this.excelRows[row].cells[col]
          if (cell.content && cell.content.trim()) {
            contentParts.push(cell.content.trim())
          }
        }
      }

      mergedContent = contentParts.join(' ')

      // 执行合并
      const rowSpan = maxRow - minRow + 1
      const colSpan = maxCol - minCol + 1

      // 设置主单元格（左上角）
      const mainCell = this.excelRows[minRow].cells[minCol]
      this.$set(this.excelRows[minRow].cells, minCol, {
        ...mainCell,
        content: mergedContent,
        merged: true,
        mergeMain: true,
        mergeRowSpan: rowSpan,
        mergeColSpan: colSpan,
        hidden: false
      })

      // 隐藏其他单元格（使用visibility:hidden而不是display:none）
      for (let row = minRow; row <= maxRow; row++) {
        for (let col = minCol; col <= maxCol; col++) {
          if (row === minRow && col === minCol) continue // 跳过主单元格

          const cell = this.excelRows[row].cells[col]
          this.$set(this.excelRows[row].cells, col, {
            ...cell,
            content: '',
            type: '',
            fieldType: '',
            fieldCode: '',
            merged: true,
            mergeMain: false,
            mergeRowSpan: 1,
            mergeColSpan: 1,
            hidden: true
          })
        }
      }

      this.selectedCells = []
      this.hideContextMenu()
      this.$message.success(`已合并 ${rowSpan}×${colSpan} 个单元格`)
      // Force re-render to update the overlay
      this.$forceUpdate()
    },

    // 拆分单元格
    splitCells() {
      if (!this.hasSelectedMergedCell) {
        this.$message.warning('请选择已合并的单元格进行拆分')
        return
      }

      let splitCount = 0
      const processedCells = new Set()

      this.selectedCells.forEach(cellKey => {
        if (processedCells.has(cellKey)) return

        const [rowIndex, colIndex] = cellKey.split('-').map(Number)
        const cell = this.excelRows[rowIndex].cells[colIndex]

        if (cell.merged) {
          if (cell.mergeMain) {
            // 这是主合并单元格，执行拆分
            this.splitSingleMergedCell(rowIndex, colIndex)
            splitCount++
          } else {
            // 这是被合并的单元格，找到主单元格
            const mainCell = this.findMainMergedCell(rowIndex, colIndex)
            if (mainCell && !processedCells.has(`${mainCell.row}-${mainCell.col}`)) {
              this.splitSingleMergedCell(mainCell.row, mainCell.col)
              splitCount++
              processedCells.add(`${mainCell.row}-${mainCell.col}`)
            }
          }
          processedCells.add(cellKey)
        }
      })

      this.selectedCells = []
      this.hideContextMenu()
      if (splitCount > 0) {
        this.$message.success(`已拆分 ${splitCount} 个合并单元格`)
        // Force re-render to update the overlay
        this.$forceUpdate()
      }
    },

    // 拆分单个合并单元格
    splitSingleMergedCell(rowIndex, colIndex) {
      const mainCell = this.excelRows[rowIndex].cells[colIndex]

      if (!mainCell.merged || !mainCell.mergeMain) {
        return
      }

      const rowSpan = mainCell.mergeRowSpan
      const colSpan = mainCell.mergeColSpan

      // 恢复主单元格
      this.$set(this.excelRows[rowIndex].cells, colIndex, {
        ...mainCell,
        merged: false,
        mergeMain: false,
        mergeRowSpan: 1,
        mergeColSpan: 1,
        hidden: false
      })

      // 恢复被隐藏的单元格
      for (let row = rowIndex; row < rowIndex + rowSpan; row++) {
        for (let col = colIndex; col < colIndex + colSpan; col++) {
          if (row === rowIndex && col === colIndex) continue // 跳过主单元格

          const cell = this.excelRows[row].cells[col]
          this.$set(this.excelRows[row].cells, col, {
            ...cell,
            content: '',
            type: '',
            fieldType: '',
            fieldCode: '',
            merged: false,
            mergeMain: false,
            mergeRowSpan: 1,
            mergeColSpan: 1,
            hidden: false
          })
        }
      }
    },

    // 拆分单个单元格（从按钮触发）
    splitSingleCell(rowIndex, colIndex) {
      this.splitSingleMergedCell(rowIndex, colIndex)
      this.$message.success('已拆分合并单元格')
      // Force re-render to update the overlay
      this.$forceUpdate()
    },

    // 查找主合并单元格
    findMainMergedCell(rowIndex, colIndex) {
      // 向左上方搜索主单元格
      for (let row = 0; row <= rowIndex; row++) {
        for (let col = 0; col <= colIndex; col++) {
          const cell = this.excelRows[row].cells[col]
          if (cell.merged && cell.mergeMain) {
            const endRow = row + cell.mergeRowSpan - 1
            const endCol = col + cell.mergeColSpan - 1

            // 检查当前单元格是否在这个合并区域内
            if (rowIndex >= row && rowIndex <= endRow &&
                colIndex >= col && colIndex <= endCol) {
              return { row, col }
            }
          }
        }
      }
      return null
    },

    // 显示右键菜单
    showCellContextMenu(event, rowIndex, colIndex) {
      console.log('右键菜单触发:', { rowIndex, colIndex, clientX: event.clientX, clientY: event.clientY })

      event.preventDefault()

      // 如果右键的单元格没有被选中，则选中它
      const cellKey = `${rowIndex}-${colIndex}`
      if (!this.selectedCells.includes(cellKey)) {
        this.selectedCells = [cellKey]
      }

      // 设置菜单位置和显示状态
      this.contextMenuX = event.clientX
      this.contextMenuY = event.clientY
      this.contextMenuVisible = true

      console.log('菜单状态设置完成:', {
        visible: this.contextMenuVisible,
        x: this.contextMenuX,
        y: this.contextMenuY,
        selectedCells: this.selectedCells.length
      })

      // 添加点击其他地方隐藏菜单的监听器
      this.$nextTick(() => {
        const hideMenu = (e) => {
          if (!e.target.closest('.context-menu')) {
            this.hideContextMenu()
            document.removeEventListener('click', hideMenu)
          }
        }
        document.addEventListener('click', hideMenu)
      })
    },

    // 隐藏右键菜单
    hideContextMenu() {
      console.log('隐藏右键菜单')
      this.contextMenuVisible = false
      this.contextMenuX = 0
      this.contextMenuY = 0
    },

    // 测试右键菜单显示
    testContextMenu() {
      console.log('测试右键菜单')
      this.selectedCells = ['0-0']
      this.contextMenuX = 200
      this.contextMenuY = 200
      this.contextMenuVisible = true
      console.log('菜单状态:', {
        visible: this.contextMenuVisible,
        x: this.contextMenuX,
        y: this.contextMenuY
      })
    },

    // 编辑选中的单元格
    editSelectedCell() {
      if (this.selectedCells.length === 1) {
        const [rowIndex, colIndex] = this.selectedCells[0].split('-').map(Number)
        this.editCell(rowIndex, colIndex)
      }
      this.hideContextMenu()
    },

    // 清空选中的单元格
    clearSelectedCells() {
      this.selectedCells.forEach(cellKey => {
        const [rowIndex, colIndex] = cellKey.split('-').map(Number)
        this.clearCell(rowIndex, colIndex)
      })
      this.selectedCells = []
      this.hideContextMenu()
    },

    // 获取行样式
    getRowStyle(rowIndex) {
      // 默认行高
      return {
        minHeight: '35px'
      };
    },

    // 获取单元格样式
    getCellStyle(cell, colIndex) {
      const baseStyle = {
        width: this.columnWidths[colIndex] + 'px',
        minHeight: '35px',
        boxSizing: 'border-box'
      }

      // 如果是合并的主单元格，设置跨度
      if (cell.merged && cell.mergeMain) {
        // 计算合并单元格的总宽度（横向合并）
        let totalWidth = 0
        const colSpan = cell.mergeColSpan || 1
        for (let i = 0; i < colSpan; i++) {
          totalWidth += this.columnWidths[colIndex + i] || 90
        }

        baseStyle.width = totalWidth + 'px'
        // 不再设置height和minHeight to prevent row expansion
        // The visual merge effect is now handled by the overlay layer
        baseStyle.zIndex = 20

        console.log(`合并单元格样式 ${this.getColumnLabel(colIndex)}${this.getCurrentRowIndex(cell) + 1}:`, {
          colSpan,
          width: totalWidth,
          merged: cell.merged,
          mergeMain: cell.mergeMain
        })
      }

      return baseStyle
    },

    // 获取当前单元格的行索引（用于调试）
    getCurrentRowIndex(targetCell) {
      for (let rowIndex = 0; rowIndex < this.excelRows.length; rowIndex++) {
        for (let colIndex = 0; colIndex < this.excelRows[rowIndex].cells.length; colIndex++) {
          if (this.excelRows[rowIndex].cells[colIndex] === targetCell) {
            return rowIndex
          }
        }
      }
      return 0
    },

    // 获取列的左侧位置
    getColumnLeftPosition(colIndex) {
      let left = 40; // 行号列的宽度
      for (let i = 0; i < colIndex; i++) {
        left += this.columnWidths[i] || 90;
      }
      return left;
    },

    // 获取合并覆盖层样式
    getMergedOverlayStyle(rowIndex, colIndex) {
      const cell = this.excelRows[rowIndex].cells[colIndex];
      if (!cell.merged || !cell.mergeMain) return {};

      // 计算合并单元格的总宽度（横向合并）
      let totalWidth = 0;
      const colSpan = cell.mergeColSpan || 1;
      for (let i = 0; i < colSpan; i++) {
        totalWidth += this.columnWidths[colIndex + i] || 90;
      }

      // 计算合并单元格的总高度（纵向合并）
      const rowSpan = cell.mergeRowSpan || 1;
      const totalHeight = rowSpan * 35; // 每行高度35px

      // 计算绝对定位的坐标
      const top = rowIndex * 35 + 30; // 加上列标题高度30px
      const left = this.getColumnLeftPosition(colIndex);

      return {
        position: 'absolute',
        top: top + 'px',
        left: left + 'px',
        width: totalWidth + 'px',
        height: totalHeight + 'px',
        zIndex: 25,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        // 确保垂直合并时覆盖层正确显示
        pointerEvents: 'auto',
        cursor: 'pointer'
      };
    },

    // 编辑单元格
    editCell(rowIndex, colIndex) {
      const cell = this.excelRows[rowIndex].cells[colIndex]
      this.editingCell = {
        content: cell.content,
        type: cell.type || 'label'
      }
      this.editingCellPosition = { rowIndex, colIndex }
      this.cellEditVisible = true
    },

    // 确认单元格编辑
    confirmCellEdit() {
      if (this.editingCellPosition) {
        const { rowIndex, colIndex } = this.editingCellPosition
        this.excelRows[rowIndex].cells[colIndex].content = this.editingCell.content
        this.excelRows[rowIndex].cells[colIndex].type = this.editingCell.type
        this.cellEditVisible = false
        this.$message.success('单元格编辑成功')
      }
    },

    // 清空单元格
    clearCell(rowIndex, colIndex) {
      console.log('清空单元格:', rowIndex, colIndex)

      const cell = this.excelRows[rowIndex].cells[colIndex]

      // 保留合并相关属性，只清空内容
      this.$set(this.excelRows[rowIndex].cells, colIndex, {
        ...cell,
        content: '',
        type: '',
        fieldType: '',
        fieldCode: ''
      })

      // 强制更新视图
      this.$forceUpdate()

      this.$message.success(`已清空单元格 ${this.getColumnLabel(colIndex)}${rowIndex + 1}`)
    },

    // 测试清空功能
    testClearFunction() {
      // 先添加一个测试内容
      this.$set(this.excelRows[0].cells, 0, {
        content: '测试内容',
        type: 'label',
        fieldType: 'text',
        fieldCode: 'test'
      })

      this.$message.info('已在A1添加测试内容，请点击删除按钮测试')
    },

    // 加载模板数据
    async loadTemplate(templateId) {
      try {
        const response = await getExcelTemplate(templateId)

        if (response.code === 200 && response.data) {
          const template = response.data
          console.log('加载的模板数据:', template)
          console.log('模板是否有sheets字段:', !!template.sheets)
          console.log('模板sheets数量:', template.sheets ? template.sheets.length : 0)

          // 设置模板基本信息
          this.templateInfo.name = template.templateName
          this.templateInfo.code = template.templateCode
          this.templateInfo.deviceType = template.deviceType || ''
          this.currentTemplateId = template.id

          // 检查是否有多sheet数据
          if (template.sheets && template.sheets.length > 0) {
            console.log('使用多sheet数据加载')
            // 加载多sheet数据
            this.loadMultiSheetData(template)
          } else {
            console.log('使用单sheet数据加载（兼容模式）')
            // 兼容旧版单sheet数据
            this.loadSingleSheetData(template)
          }

          this.$message.success('模板加载成功')
        } else {
          this.$message.error(response.msg || '模板加载失败')
        }
      } catch (error) {
        console.error('加载模板失败:', error)
        this.$message.error('加载模板失败: ' + (error.msg || error.message || '网络错误'))
      }
    },

    // 加载多sheet数据
    loadMultiSheetData(template) {
      this.sheets = []

      template.sheets.forEach(sheetData => {
        console.log('处理sheet数据:', sheetData)

        // 处理列宽度数据
        let columnWidths = [90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90]
        if (sheetData.columnWidths) {
          if (Array.isArray(sheetData.columnWidths)) {
            columnWidths = sheetData.columnWidths
          } else if (typeof sheetData.columnWidths === 'string') {
            try {
              columnWidths = JSON.parse(sheetData.columnWidths)
            } catch (e) {
              console.warn('解析列宽度失败:', e)
            }
          }
        }

        const sheet = {
          id: sheetData.sheetId || sheetData.id, // 兼容两种字段名
          name: sheetData.sheetName || sheetData.name, // 兼容两种字段名
          maxColumns: sheetData.maxColumns || 20,
          maxRows: sheetData.maxRows || 30,
          columnWidths: columnWidths,
          excelRows: []
        }

        // 初始化sheet的行数据
        for (let i = 0; i < sheet.maxRows; i++) {
          const row = { cells: [] }
          for (let j = 0; j < sheet.maxColumns; j++) {
            row.cells.push({
              content: '',
              type: '',
              fieldType: '',
              fieldCode: '',
              // 合并单元格相关属性
              merged: false,
              mergeMain: false,
              mergeRowSpan: 1,
              mergeColSpan: 1,
              hidden: false
            })
          }
          sheet.excelRows.push(row)
        }

        // 加载单元格数据
        console.log(`加载Sheet ${sheet.name} 的单元格数据`)
        console.log('Sheet数据结构:', sheetData)

        // 获取单元格数据，可能在不同的字段中
        let cellsData = sheetData.cells || []

        if (cellsData && cellsData.length > 0) {
          console.log(`Sheet ${sheet.name} 有 ${cellsData.length} 个单元格`)
          cellsData.forEach(cell => {
            if (cell.rowIndex < sheet.maxRows && cell.colIndex < sheet.maxColumns) {
              const cellData = {
                content: cell.content,
                type: cell.cellType,
                fieldType: cell.fieldType,
                fieldCode: cell.fieldCode,
                // 合并单元格相关属性（从数据库加载或使用默认值）
                merged: cell.merged || false,
                mergeMain: cell.mergeMain || false,
                mergeRowSpan: cell.mergeRowSpan || 1,
                mergeColSpan: cell.mergeColSpan || 1,
                hidden: cell.hidden || false
              }

              sheet.excelRows[cell.rowIndex].cells[cell.colIndex] = cellData

              console.log(`设置单元格 [${cell.rowIndex}, ${cell.colIndex}] = "${cell.content}"`, {
                position: `${this.getColumnLabel(cell.colIndex)}${cell.rowIndex + 1}`,
                merged: cellData.merged,
                mergeMain: cellData.mergeMain,
                mergeRowSpan: cellData.mergeRowSpan,
                mergeColSpan: cellData.mergeColSpan,
                hidden: cellData.hidden
              })
            } else {
              console.warn('单元格位置超出范围:', cell.rowIndex, cell.colIndex, sheet.maxRows, sheet.maxColumns)
            }
          })
        } else {
          console.log(`Sheet ${sheet.name} 没有单元格数据`)
        }

        this.sheets.push(sheet)
      })

      // 设置活动sheet
      this.activeSheetId = template.activeSheetId || this.sheets[0].id
      const activeSheet = this.sheets.find(s => s.id === this.activeSheetId) || this.sheets[0]
      this.loadSheetData(activeSheet)
    },

    // 加载单sheet数据（兼容旧版）
    loadSingleSheetData(template) {
      // 创建单个sheet
      const sheet = {
        id: 'sheet_1',
        name: 'Sheet1',
        maxColumns: template.maxColumns || 20,
        maxRows: template.maxRows || 30,
        columnWidths: [],
        excelRows: []
      }

      // 设置列宽度
      if (template.columnWidths) {
        try {
          sheet.columnWidths = JSON.parse(template.columnWidths)
        } catch (e) {
          console.warn('解析列宽度失败:', e)
          sheet.columnWidths = [90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90]
        }
      } else {
        sheet.columnWidths = [90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90]
      }

      // 初始化sheet的行数据
      for (let i = 0; i < sheet.maxRows; i++) {
        const row = { cells: [] }
        for (let j = 0; j < sheet.maxColumns; j++) {
          row.cells.push({
            content: '',
            type: '',
            fieldType: '',
            fieldCode: '',
            // 合并单元格相关属性
            merged: false,
            mergeMain: false,
            mergeRowSpan: 1,
            mergeColSpan: 1,
            hidden: false
          })
        }
        sheet.excelRows.push(row)
      }

      // 加载单元格数据
      if (template.cells && template.cells.length > 0) {
        template.cells.forEach(cell => {
          if (cell.rowIndex < sheet.maxRows && cell.colIndex < sheet.maxColumns) {
            sheet.excelRows[cell.rowIndex].cells[cell.colIndex] = {
              content: cell.content,
              type: cell.cellType,
              fieldType: cell.fieldType,
              fieldCode: cell.fieldCode,
              // 合并单元格相关属性
              merged: cell.merged || false,
              mergeMain: cell.mergeMain || false,
              mergeRowSpan: cell.mergeRowSpan || 1,
              mergeColSpan: cell.mergeColSpan || 1,
              hidden: cell.hidden || false
            }
          }
        })
      }

      this.sheets = [sheet]
      this.activeSheetId = sheet.id
      this.loadSheetData(sheet)
    },

    // 清空当前sheet
    clearAll() {
      this.$confirm('确定要清空当前Sheet的所有内容吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 清空当前sheet的数据
        this.initializeExcelSheet()

        // 同时更新sheets数组中的数据
        const currentSheet = this.sheets.find(s => s.id === this.activeSheetId)
        if (currentSheet) {
          currentSheet.excelRows = JSON.parse(JSON.stringify(this.excelRows))
        }

        this.$message.success('已清空当前Sheet')
      })
    },

    // 导出Excel
    async handleExportExcel() {
      try {
        const templateData = this.generateTemplateData()

        if (!this.currentTemplateId) {
          this.$message.warning('请先保存模板再导出Excel')
          return
        }

        // 调用导出API
        const response = await exportBlankExcelTemplate(this.currentTemplateId)

        // 创建下载链接
        const blob = new Blob([response], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        })

        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `${templateData.templateName || '模板'}.xlsx`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)

        this.$message.success('Excel导出成功')
      } catch (error) {
        console.error('导出Excel失败:', error)
        this.$message.error('导出失败: ' + (error.msg || error.message || '网络错误'))
      }
    },

    // 预览
    handlePreview() {
      const templateData = this.generateTemplateData()
      console.log('预览模板:', templateData)
      this.$message.info('预览功能')
    },

    // 保存
    async handleSave() {
      try {
        // 验证合并单元格数据一致性
        this.validateMergedCellsData()

        const templateData = this.generateTemplateData()
        console.log('保存模板:', templateData)
        console.log('保存的sheets数量:', templateData.sheets ? templateData.sheets.length : 0)
        console.log('保存的sheets详情:', templateData.sheets)
        console.log('保存的所有单元格数量:', templateData.cells ? templateData.cells.length : 0)
        console.log('保存的单元格详情:', templateData.cells)

        // 验证模板编码唯一性
        if (!this.currentTemplateId) {
          const checkResult = await checkTemplateCodeUnique(templateData.templateCode)
          if (checkResult.code !== 200 || !checkResult.data) {
            this.$message.error('模板编码已存在，请修改后重试')
            return
          }
        }

        // 调用后端API保存模板
        const response = await saveExcelTemplate(templateData)

        if (response.code === 200) {
          // 保存成功后设置模板ID
          if (response.data && response.data.id) {
            this.currentTemplateId = response.data.id
          }
          this.$message.success('模板保存成功')
          this.$emit('save', templateData)
        } else {
          this.$message.error(response.msg || '保存失败')
        }
      } catch (error) {
        console.error('保存模板失败:', error)
        this.$message.error('保存失败: ' + (error.msg || error.message || '网络错误'))
      }
    },



    // 生成模板数据
    generateTemplateData() {
      // 保存当前sheet数据
      this.saveCurrentSheetData()

      const allCells = []
      const allFields = []
      const sheetsData = []

      // 遍历所有sheets生成数据
      this.sheets.forEach((sheet, sheetIndex) => {
        const sheetCells = []
        const sheetFields = []

        // 解析每个sheet的Excel数据
        sheet.excelRows.forEach((row, rowIndex) => {
          row.cells.forEach((cell, colIndex) => {
            // 保存有内容的单元格或者合并相关的单元格
            if ((cell.content && cell.content.trim()) || cell.merged || cell.hidden) {
              // 生成单元格配置
              const cellData = {
                sheetId: sheet.id,
                sheetName: sheet.name,
                sheetIndex: sheetIndex,
                rowIndex: rowIndex,
                colIndex: colIndex,
                cellPosition: this.getColumnLabel(colIndex) + (rowIndex + 1),
                content: cell.content || '',
                cellType: cell.type || 'label',
                fieldType: cell.fieldType || 'text',
                fieldCode: cell.fieldCode || `${sheet.id}_field_${rowIndex}_${colIndex}`,
                sortOrder: rowIndex * sheet.maxColumns + colIndex,
                // 合并单元格相关属性
                merged: cell.merged || false,
                mergeMain: cell.mergeMain || false,
                mergeRowSpan: cell.mergeRowSpan || 1,
                mergeColSpan: cell.mergeColSpan || 1,
                hidden: cell.hidden || false
              }

              sheetCells.push(cellData)
              allCells.push(cellData)

              // 生成字段配置（只为有内容的标签单元格生成字段）
              if (cell.content && cell.content.trim() && cell.type === 'label' && cell.fieldType !== 'header') {
                const fieldData = {
                  sheetId: sheet.id,
                  sheetName: sheet.name,
                  fieldName: cell.content,
                  fieldCode: cell.fieldCode || `${sheet.id}_field_${rowIndex}_${colIndex}`,
                  fieldType: cell.fieldType || 'text',
                  fieldCategory: cell.fieldType === 'header' ? 'table' : 'basic',
                  isRequired: 0,
                  defaultValue: '',
                  validationRule: '',
                  sortOrder: allFields.length
                }

                sheetFields.push(fieldData)
                allFields.push(fieldData)
              }
            }
          })
        })

        // 保存每个sheet的数据
        sheetsData.push({
          sheetId: sheet.id,
          sheetName: sheet.name,
          maxColumns: sheet.maxColumns,
          maxRows: sheet.maxRows,
          columnWidths: sheet.columnWidths,
          cells: sheetCells,
          fields: sheetFields
        })
      })

      const templateData = {
        templateName: this.templateInfo.name,
        templateCode: this.templateInfo.code,
        deviceType: this.templateInfo.deviceType,
        description: '通过Excel设计器创建的多Sheet模板',
        maxColumns: this.maxColumns,
        maxRows: this.maxRows,
        columnWidths: this.columnWidths,
        status: 1,
        cells: allCells,
        fields: allFields,
        sheets: sheetsData, // 新增：多sheet数据
        activeSheetId: this.activeSheetId
      }

      // 如果是编辑模式，添加ID
      if (this.currentTemplateId || this.templateId) {
        templateData.id = this.currentTemplateId || this.templateId
      }

      return templateData
    }
  }
}
</script>

<style lang="scss" scoped>
.excel-style-template-designer {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: linear-gradient(180deg, #124B9A, #0D438D);

  .designer-header {
    padding: 20px;
    background: rgba(18, 75, 154, 0.3);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(5px);

    ::v-deep .el-form-item__label {
      color: #ffffff;
      font-weight: 500;
    }

    ::v-deep .el-input__inner {
      background: rgba(255, 255, 255, 0.9);
      border-color: rgba(255, 255, 255, 0.5);
      color: #333;
    }

    ::v-deep .el-select .el-input__inner {
      background: rgba(255, 255, 255, 0.9);
      border-color: rgba(255, 255, 255, 0.5);
    }
  }

  .designer-body {
    flex: 1;
    padding: 20px;
    overflow: hidden;
    max-width: 100%;

    .field-library {
      height: 100%;
      border: 1px solid rgba(255, 255, 255, 0.3);
      border-radius: 8px;
      background: rgba(18, 75, 154, 0.2);
      backdrop-filter: blur(10px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);

      .library-header {
        padding: 10px 15px;
        background: rgba(13, 67, 141, 0.4);
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 8px 8px 0 0;

        h4 {
          margin: 0;
          font-size: 14px;
          color: #ffffff;
          font-weight: 500;
        }
      }

      .library-content {
        height: calc(100% - 45px);
        overflow-y: auto;
        max-height: 600px;

        ::v-deep .el-collapse {
          border: none;
        }

        ::v-deep .el-collapse-item__header {
          background: rgba(13, 67, 141, 0.3);
          border-bottom: 1px solid rgba(255, 255, 255, 0.2);
          padding-left: 15px;
          font-size: 13px;
          color: #ffffff;
        }

        ::v-deep .el-collapse-item__content {
          padding-bottom: 0;
        }

        .field-list {
          padding: 10px;
          max-height: 300px;
          overflow-y: auto;

          .field-item {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            margin-bottom: 8px;
            background: rgba(255, 255, 255, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 6px;
            cursor: grab;
            transition: all 0.3s;
            backdrop-filter: blur(5px);

            &:hover {
              border-color: rgba(255, 255, 255, 0.6);
              box-shadow: 0 4px 12px rgba(255, 255, 255, 0.2);
              background: rgba(255, 255, 255, 0.25);
              transform: translateY(-1px);
            }

            &:active {
              cursor: grabbing;
            }

            i {
              margin-right: 8px;
              color: #ffffff;
            }

            span {
              font-size: 13px;
              color: #ffffff;
              font-weight: 500;
            }
          }
        }
      }
    }

    .excel-design-area {
      height: 100%;
      border: 1px solid rgba(255, 255, 255, 0.3);
      border-radius: 8px;
      display: flex;
      flex-direction: column;
      background: rgba(18, 75, 154, 0.2);
      backdrop-filter: blur(10px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
      max-width: 100%;
      overflow: hidden;

      // Sheet标签页样式
      .sheet-tabs {
        display: flex;
        align-items: flex-end; // 底部对齐，确保边框线一致
        padding: 10px 0 0;
        background: rgba(13, 67, 141, 0.2);
        border-bottom: 1px solid rgba(255, 255, 255, 0.3); // 整体底部边框

        ::v-deep .el-tabs {
          flex: 1;

          .el-tabs__header {
            margin: 0;

            .el-tabs__nav-wrap {
              &::after {
                background-color: transparent; // 移除tabs自带的底部边框，使用统一的边框
              }
            }

            .el-tabs__item {
              color: rgba(255, 255, 255, 0.8);
              border-left: 1px solid rgba(255, 255, 255, 0.2);
              border-right: 1px solid rgba(255, 255, 255, 0.2);
              border-top: 1px solid rgba(255, 255, 255, 0.2);
              border-bottom: 1px solid rgba(255, 255, 255, 0.3); // 添加底部边框
              background: rgba(255, 255, 255, 0.1);

              &.is-active {
                color: #ffffff;
                background: rgba(255, 255, 255, 0.2);
                border-bottom-color: transparent; // 激活状态下隐藏底部边框
              }

              &:hover {
                color: #ffffff;
                background: rgba(255, 255, 255, 0.15);
              }

              .el-icon-close {
                color: rgba(255, 255, 255, 0.6);

                &:hover {
                  color: #ffffff;
                  background: rgba(255, 255, 255, 0.2);
                }
              }
            }
          }
        }

        .add-sheet-btn {
          margin-left: 5px;
          color: rgba(255, 255, 255, 0.8);
          font-size: 12px;
          padding: 8px 12px;
          border: 1px solid rgba(255, 255, 255, 0.2);
          border-bottom: 1px solid rgba(255, 255, 255, 0.3);
          border-radius: 4px 4px 0 0; // 上方圆角，下方直角
          background: rgba(255, 255, 255, 0.1);
          height: 40px; // 与tab标签高度保持一致
          display: flex;
          align-items: center;

          &:hover {
            color: #ffffff;
            background: rgba(255, 255, 255, 0.15);
            border-color: rgba(255, 255, 255, 0.3);
            border-bottom-color: rgba(255, 255, 255, 0.3);
          }
        }
      }

      .excel-toolbar {
        padding: 10px 15px;
        background: rgba(13, 67, 141, 0.4);
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 8px 8px 0 0;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .toolbar-left {
          flex: 1;
        }

        .toolbar-right {
          display: flex;
          gap: 8px;
        }
      }

      .excel-sheet-container {
        flex: 1;
        overflow: auto;
        overflow-x: scroll;
        overflow-y: scroll;
        background: rgba(18, 75, 154, 0.1);
        position: relative;
        max-height: 500px;
        max-width: 1600px;
        width: 100%;
      }

      // 自定义滚动条样式
      .excel-sheet-container::-webkit-scrollbar {
        width: 14px;
        height: 14px;
      }

      .excel-sheet-container::-webkit-scrollbar-track {
        background: rgba(13, 67, 141, 0.3);
        border-radius: 7px;
        border: 1px solid rgba(255, 255, 255, 0.1);
      }

      .excel-sheet-container::-webkit-scrollbar-thumb {
        background: linear-gradient(180deg, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.6));
        border-radius: 7px;
        border: 1px solid rgba(255, 255, 255, 0.4);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      }

      .excel-sheet-container::-webkit-scrollbar-thumb:hover {
        background: linear-gradient(180deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.8));
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
      }

      .excel-sheet-container::-webkit-scrollbar-corner {
        background: rgba(13, 67, 141, 0.3);
        border: 1px solid rgba(255, 255, 255, 0.1);
      }

      // Firefox滚动条样式
      .excel-sheet-container {
        scrollbar-width: thin;
        scrollbar-color: rgba(255, 255, 255, 0.5) rgba(13, 67, 141, 0.3);
      }

      .excel-sheet {
        min-width: 2000px;
        min-height: 1000px;
        width: max-content;

        .column-headers {
          display: flex;
          position: sticky;
          top: 0;
          background: rgba(13, 67, 141, 0.8);
          border-bottom: 2px solid rgba(255, 255, 255, 0.3);
          z-index: 10;
          min-width: fit-content;
          width: max-content;

          .row-header-cell {
            width: 40px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(13, 67, 141, 0.6);
            border-right: 1px solid rgba(255, 255, 255, 0.3);
            font-size: 12px;
            font-weight: bold;
            color: #ffffff;
            position: sticky;
            left: 0;
            z-index: 11;
          }

          .column-header-cell {
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(13, 67, 141, 0.4);
            border-right: 1px solid rgba(255, 255, 255, 0.3);
            font-size: 12px;
            font-weight: bold;
            color: #ffffff;
            position: relative;

            .column-header-content {
              display: flex;
              align-items: center;
              justify-content: space-between;
              width: 100%;
              padding: 0 8px;

              .column-setting-icon {
                opacity: 0;
                transition: opacity 0.3s;
                cursor: pointer;
                font-size: 12px;
                color: rgba(255, 255, 255, 0.8);

                &:hover {
                  color: #ffffff;
                }
              }
            }

            &:hover .column-setting-icon {
              opacity: 1;
            }
          }
        }

        .excel-rows {
          min-width: fit-content;
          width: max-content;

          .excel-row {
            display: flex;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            min-width: fit-content;
            width: max-content;
            position: relative;
            align-items: flex-start; // 防止单元格拉伸填满行高
            // 确保行高度固定 - 这是关键，防止垂直合并影响其他行
            height: 35px;
            min-height: 35px;
            max-height: 35px;
            // 为绝对定位的合并单元格创建定位上下文
            overflow: visible;
          }
        }

        // 合并单元格覆盖层
        .merged-cells-overlay {
          position: absolute;
          top: 30px; // 列标题高度
          left: 0;
          width: 100%;
          height: calc(100% - 30px);
          pointer-events: none; // 不拦截鼠标事件
          z-index: 20; // 确保覆盖层在正确层级
          // 确保覆盖层不会干扰正常单元格布局
          overflow: visible;

          .merged-cell-overlay {
            border: 2px solid #67C23A;
            background: rgba(103, 194, 58, 0.15) !important;
            pointer-events: auto; // 允许与合并单元格交互
            position: absolute;

            .cell-content {
              width: 100%;
              height: 100%;
              display: flex;
              align-items: center;
              justify-content: center;
              text-align: center;
              word-wrap: break-word;
              padding: 2px;
              box-sizing: border-box;
            }

            .cell-actions {
              position: absolute;
              top: 2px;
              right: 2px;
              display: flex;
              gap: 2px;
              opacity: 0;
              transition: opacity 0.3s;

              .el-button {
                padding: 2px;
                font-size: 10px;
                min-width: 16px;
                height: 16px;
                background: rgba(255, 255, 255, 0.8);
                color: #606266;

                &:hover {
                  background: #ffffff;
                  color: #409EFF;
                }
              }
            }

            &:hover .cell-actions {
              opacity: 1;
            }

            // 合并单元格标识
            &::after {
              content: '';
              position: absolute;
              top: 2px;
              right: 2px;
              width: 0;
              height: 0;
              border-left: 8px solid transparent;
              border-top: 8px solid #67C23A;
              z-index: 30;
            }
          }
        }

            .row-header-cell {
              width: 40px;
              min-height: 35px;
              display: flex;
              align-items: center;
              justify-content: center;
              background: rgba(13, 67, 141, 0.6);
              border-right: 1px solid rgba(255, 255, 255, 0.3);
              font-size: 12px;
              font-weight: bold;
              color: #ffffff;
              position: sticky;
              left: 0;
              z-index: 9;
              align-self: stretch;
            }

            .excel-cell {
              min-height: 35px;
              height: 35px; // 固定高度防止被拉伸
              max-height: 35px;
              border-right: 1px solid rgba(255, 255, 255, 0.3);
              position: relative;
              cursor: pointer;
              transition: all 0.2s;
              overflow: visible; // 允许合并单元格溢出
              background: rgba(18, 75, 154, 0.05);
              box-sizing: border-box;
              display: flex;
              flex-direction: column;

              // 合并主单元格的特殊样式 - 重要：不在这里使用绝对定位
              // 绝对定位由覆盖层处理，这里保持正常布局流
              &.merged.merge-main {
                // 保持正常文档流位置，由覆盖层显示视觉效果
                position: relative;
                z-index: 5; // 基础层级，覆盖层会处理显示
              }

              &:hover {
                background-color: rgba(255, 255, 255, 0.1);
              }

              &.has-content {
                background-color: rgba(255, 255, 255, 0.1);

                &.is-label {
                  background-color: rgba(33, 150, 243, 0.3);
                  border-left: 3px solid #2196f3;
                }

                &.is-value {
                  background-color: rgba(156, 39, 176, 0.3);
                  border-left: 3px solid #9c27b0;
                }

                &.is-header {
                  background-color: rgba(255, 152, 0, 0.3);
                  border-left: 3px solid #ff9800;
                  font-weight: bold;
                }
              }

              &.drag-over {
                background-color: #c8e6c9;
                border: 2px dashed #4caf50;
              }

              &.drag-over-merged {
                background: linear-gradient(135deg,
                  rgba(76, 175, 80, 0.3) 0%,
                  rgba(76, 175, 80, 0.1) 50%,
                  rgba(76, 175, 80, 0.3) 100%);
                border: 3px dashed #4caf50;
                box-shadow:
                  0 0 10px rgba(76, 175, 80, 0.5),
                  inset 0 0 20px rgba(76, 175, 80, 0.2);
                animation: mergedCellPulse 1.5s ease-in-out infinite;
                position: relative;

                &::before {
                  content: 'Label放置在此';
                  position: absolute;
                  top: 2px;
                  left: 4px;
                  font-size: 10px;
                  color: #2e7d32;
                  font-weight: bold;
                  background: rgba(255, 255, 255, 0.9);
                  padding: 1px 4px;
                  border-radius: 2px;
                  z-index: 10;
                }

                &::after {
                  content: 'Value放置在右侧';
                  position: absolute;
                  bottom: 2px;
                  right: 4px;
                  font-size: 10px;
                  color: #2e7d32;
                  font-weight: bold;
                  background: rgba(255, 255, 255, 0.9);
                  padding: 1px 4px;
                  border-radius: 2px;
                  z-index: 10;
                }
              }

              &.drag-preview-value {
                background: linear-gradient(45deg,
                  rgba(156, 39, 176, 0.2) 0%,
                  rgba(156, 39, 176, 0.1) 50%,
                  rgba(156, 39, 176, 0.2) 100%);
                border: 2px dashed #9c27b0;
                animation: valuePreviewPulse 2s ease-in-out infinite;
                position: relative;

                &::before {
                  content: 'Value将放置在此';
                  position: absolute;
                  top: 50%;
                  left: 50%;
                  transform: translate(-50%, -50%);
                  font-size: 10px;
                  color: #7b1fa2;
                  font-weight: bold;
                  background: rgba(255, 255, 255, 0.95);
                  padding: 2px 6px;
                  border-radius: 3px;
                  white-space: nowrap;
                  z-index: 10;
                  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
                }
              }

              &.selected {
                background: rgba(64, 158, 255, 0.3) !important;
                border: 2px solid #409EFF !important;
                box-shadow: 0 0 5px rgba(64, 158, 255, 0.5);
                z-index: 10;
              }

              &.merged {
                border: 2px solid #67C23A;

                &.merge-main {
                  // 主合并单元格现在由覆盖层显示，这里只需要保持基本样式
                  background: rgba(103, 194, 58, 0.15) !important;
                  position: relative;
                  z-index: 20;

                  // 内容居中显示
                  .cell-content {
                    width: 100%;
                    height: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    text-align: center;
                    word-wrap: break-word;
                    padding: 2px;
                    box-sizing: border-box;
                  }

                  // 合并单元格标识
                  &::after {
                    content: '';
                    position: absolute;
                    top: 2px;
                    right: 2px;
                    width: 0;
                    height: 0;
                    border-left: 8px solid transparent;
                    border-top: 8px solid #67C23A;
                    z-index: 30;
                  }
                }

                // 被合并的单元格（隐藏状态）
                &:not(.merge-main) {
                  // 被合并的单元格应该是透明的
                  background: rgba(103, 194, 58, 0.05) !important;
                  border-color: rgba(103, 194, 58, 0.3);

                  // 显示合并指示器
                  &::before {
                    content: '[合并]';
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    font-size: 8px;
                    color: #67C23A;
                    opacity: 0.6;
                  }
                }
              }

              // 被隐藏的单元格（被合并覆盖的单元格）
              &.hidden {
                // 保持单元格在文档流中但使其不可见
                visibility: hidden !important;
                opacity: 0 !important;

                // 保持尺寸但移除内容
                background: transparent !important;
                border-color: transparent !important;
                color: transparent !important;

                // 确保不影响布局
                pointer-events: none !important;
              }

              .cell-content {
                flex: 1;
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 0 6px;
                width: 100%;
                box-sizing: border-box;

                .cell-text {
                  flex: 1;
                  font-size: 12px;
                  color: #ffffff;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                  min-width: 0;
                  max-width: calc(100% - 40px);
                  font-weight: 500;
                }

                .cell-actions {
                  opacity: 0;
                  transition: opacity 0.3s;
                  display: flex;
                  gap: 2px;
                  flex-shrink: 0;
                  width: 36px;
                  justify-content: flex-end;

                  .el-button {
                    padding: 2px;
                    font-size: 10px;
                    min-width: 16px;
                    height: 16px;
                    pointer-events: auto;
                  }
                }

                &:hover .cell-actions {
                  opacity: 1;
                }
              }

              .empty-cell {
                flex: 1;
                display: flex;
                align-items: center;
                justify-content: center;

                .cell-placeholder {
                  font-size: 10px;
                  color: rgba(255, 255, 255, 0.5);

                  .merge-indicator {
                    color: #67C23A;
                    font-weight: bold;
                    margin-left: 4px;
                  }
                }
              }
            }
          }
        }
      }
    }

    // 工具栏选择信息样式
    .selection-info {
      color: rgba(255, 255, 255, 0.8);
      font-size: 12px;
      margin-right: 10px;
      padding: 4px 8px;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 4px;
    }
  

  // 右键菜单样式
  .context-menu {
    position: fixed;
    background: #ffffff;
    border: 2px solid #409EFF;
    border-radius: 4px;
    box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.3);
    z-index: 99999;
    min-width: 150px;
    padding: 8px 0;

    .context-menu-item {
      padding: 8px 16px;
      font-size: 14px;
      color: #606266;
      cursor: pointer;
      display: flex;
      align-items: center;
      transition: background-color 0.3s;

      i {
        margin-right: 8px;
        width: 16px;
      }

      &:hover {
        background-color: #f5f7fa;
        color: #409EFF;
      }
    }

    .context-menu-divider {
      height: 1px;
      background-color: #e4e7ed;
      margin: 4px 0;
    }
  }

  // 合并单元格拖拽动画
  @keyframes mergedCellPulse {
    0% {
      box-shadow:
        0 0 10px rgba(76, 175, 80, 0.5),
        inset 0 0 20px rgba(76, 175, 80, 0.2);
    }
    50% {
      box-shadow:
        0 0 20px rgba(76, 175, 80, 0.8),
        inset 0 0 30px rgba(76, 175, 80, 0.4);
    }
    100% {
      box-shadow:
        0 0 10px rgba(76, 175, 80, 0.5),
        inset 0 0 20px rgba(76, 175, 80, 0.2);
    }
  }

  // 值单元格预览动画
  @keyframes valuePreviewPulse {
    0% {
      border-color: #9c27b0;
      background: linear-gradient(45deg,
        rgba(156, 39, 176, 0.2) 0%,
        rgba(156, 39, 176, 0.1) 50%,
        rgba(156, 39, 176, 0.2) 100%);
    }
    50% {
      border-color: #e91e63;
      background: linear-gradient(45deg,
        rgba(233, 30, 99, 0.3) 0%,
        rgba(233, 30, 99, 0.15) 50%,
        rgba(233, 30, 99, 0.3) 100%);
    }
    100% {
      border-color: #9c27b0;
      background: linear-gradient(45deg,
        rgba(156, 39, 176, 0.2) 0%,
        rgba(156, 39, 176, 0.1) 50%,
        rgba(156, 39, 176, 0.2) 100%);
    }
  }



  .debug-panel {
    position: fixed !important;
    bottom: 0 !important;
    left: 0 !important;
    width: 100% !important;
    z-index: 9999 !important;
    background: rgba(18, 75, 154, 0.98) !important;
    backdrop-filter: blur(15px);
    border-top: 2px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.4);
    display: block !important;

    .debug-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 20px;
      background: rgba(13, 67, 141, 0.9);
      color: #ffffff;
      border-bottom: 1px solid rgba(255, 255, 255, 0.2);

      .debug-title {
        display: flex;
        align-items: center;
        font-size: 16px;
        font-weight: 600;

        i {
          margin-right: 8px;
          font-size: 18px;
        }
      }

      .debug-actions {
        display: flex;
        gap: 10px;

        .el-button {
          color: rgba(255, 255, 255, 0.9);

          &:hover {
            color: #ffffff;
          }
        }
      }
    }

    .debug-content {
      padding: 20px;
      height: 450px;
      overflow-y: auto;
      transition: height 0.3s ease;

      &.expanded {
        height: 75vh;

        .json-container {
          height: 500px;
        }
      }
    }

    ::v-deep .el-collapse {
      background: rgba(18, 75, 154, 0.95);
      border-radius: 8px;
      backdrop-filter: blur(15px);
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
      border: 1px solid rgba(255, 255, 255, 0.4);
    }

    .debug-card {
      height: 100%;

      ::v-deep .el-card {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 8px;
        height: 100%;
        transition: all 0.3s ease;

        &:hover {
          border-color: rgba(255, 255, 255, 0.5);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }
      }

      // 左侧信息卡片固定高度
      &:not(.json-card) {
        height: auto;

        ::v-deep .el-card {
          height: auto;
        }

        ::v-deep .el-card__body {
          min-height: 160px;
        }
      }

      ::v-deep .el-card__header {
        background: rgba(13, 67, 141, 0.6);
        color: #ffffff;
        font-weight: 600;
        border-bottom: 1px solid rgba(255, 255, 255, 0.3);
        display: flex;
        align-items: center;

        i {
          margin-right: 8px;
          font-size: 16px;
        }
      }

      ::v-deep .el-card__body {
        padding: 15px;
        height: calc(100% - 60px);
        overflow-y: auto;
      }

      .debug-info {
        p {
          margin: 8px 0;
          color: #ffffff;
          font-size: 13px;
          line-height: 1.4;

          &:first-child {
            margin-top: 0;
          }

          &:last-child {
            margin-bottom: 0;
          }
        }
      }

      .json-viewer {
        height: 100%;

        ::v-deep .el-tabs {
          height: 100%;

          .el-tabs__header {
            margin: 0 0 10px 0;
          }

          .el-tabs__content {
            height: calc(100% - 50px);
            overflow: hidden;
          }

          .el-tab-pane {
            height: 100%;
            overflow: hidden;
          }
        }

        .json-container {
          height: 300px;
          overflow-x: hidden;
          overflow-y: scroll;
          border: 1px solid rgba(255, 255, 255, 0.2);
          border-radius: 6px;
          background: rgba(0, 0, 0, 0.3);
        }

        // 自定义滚动条样式 - 只显示纵向滚动条
        .json-container::-webkit-scrollbar {
          width: 12px;
          height: 0px; // 隐藏横向滚动条
        }

        .json-container::-webkit-scrollbar-track {
          background: rgba(255, 255, 255, 0.1);
          border-radius: 6px;
        }

        .json-container::-webkit-scrollbar-thumb {
          background: rgba(255, 255, 255, 0.4);
          border-radius: 6px;
          border: 2px solid rgba(0, 0, 0, 0.3);
        }

        .json-container::-webkit-scrollbar-thumb:hover {
          background: rgba(255, 255, 255, 0.6);
        }

        // Firefox滚动条样式
        .json-container {
          scrollbar-width: auto;
          scrollbar-color: rgba(255, 255, 255, 0.4) rgba(255, 255, 255, 0.1);
        }

        pre {
          background: transparent;
          padding: 15px;
          font-size: 11px;
          line-height: 1.5;
          color: #e8e8e8;
          margin: 0;
          white-space: pre-wrap;
          word-wrap: break-word;
          min-height: 100%;
          font-family: 'Consolas', 'Monaco', 'Courier New', monospace;

          // JSON语法高亮样式
          .json-key {
            color: #79c0ff;
          }

          .json-string {
            color: #a5d6ff;
          }

          .json-number {
            color: #79c0ff;
          }

          .json-boolean {
            color: #ff7b72;
          }

          .json-null {
            color: #8b949e;
          }
        }
      }

      // JSON卡片特殊样式
      &.json-card {
        ::v-deep .el-card__body {
          padding: 10px;
        }
      }
    }
  }

  .width-editor {
    background: rgba(18, 75, 154, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 6px;
    padding: 5px;
    border: 1px solid rgba(255, 255, 255, 0.3);

    .el-form-item {
      margin-bottom: 0;

      ::v-deep .el-form-item__label {
        color: #ffffff;
      }
    }

    .el-input-number {
      width: 120px;
    }
  }

  // 全局弹出框样式优化
  ::v-deep .el-popover {
    background: rgba(18, 75, 154, 0.95) !important;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
    color: #ffffff;
  }

  ::v-deep .el-dialog {
    background: rgba(18, 75, 154, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
    color: #ffffff;
  }

  ::v-deep .el-dialog__header {
    background: rgba(13, 67, 141, 0.8);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);

    .el-dialog__title {
      color: #ffffff;
    }
  }

  .debug-toggle-bottom {
    position: fixed;
    bottom: 20px;
    left: 20px;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    align-items: center;

    .el-button {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(0, 0, 0, 0.4);
      }

      i {
        font-size: 20px;
      }
    }

    .debug-tooltip {
      margin-top: 8px;
      padding: 4px 8px;
      background: rgba(0, 0, 0, 0.8);
      color: #ffffff;
      font-size: 12px;
      border-radius: 4px;
      white-space: nowrap;
      opacity: 0;
      transform: translateY(10px);
      transition: all 0.3s ease;
      pointer-events: none;
    }

    &:hover .debug-tooltip {
      opacity: 1;
      transform: translateY(0);
    }
  }

  // 调试面板滑入动画
  .debug-slide-enter-active,
  .debug-slide-leave-active {
    transition: transform 0.3s ease;
  }

  .debug-slide-enter,
  .debug-slide-leave-to {
    transform: translateY(100%);
  }

</style>
